{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/shared/**/*",
    "src/main/*.ts",
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.tsx",
    "src/preload/*.d.ts"
  ],
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "noUnusedLocals": false,
    "types": ["vitest/globals", "@testing-library/jest-dom"],
    "baseUrl": ".",
    "paths": {
      "@renderer/*": [
        "src/renderer/src/*"
      ],
      "~/*": [
        "src/renderer/src/*"
      ],
      "@shared/*": [
        "src/shared/*"
      ],
    }
  }
}
